'use client';

import { useState, useEffect } from 'react';
import { Link } from '@/i18n/navigation';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Plus, Sparkles, ArrowLeft } from 'lucide-react';
import { getBranchWithShop } from '@/mock/shopData';
import { AppLoading } from '@/components/ui/app-loading';
import { MenuItemImage } from '@/components/ui/image-with-fallback';
import React from 'react';

// Mock menu items
const initialMenuItems = [
  {
    id: '1',
    name: 'Margherita Pizza',
    description: 'Classic pizza with fresh basil, mozzarella, and tomato sauce.',
    price: 12.99,
    category: 'mainCourse',
    image: '/images/menu/margherita-pizza.jpg',
    available: true,
  },
  {
    id: '2',
    name: 'Caesar Salad',
    description: 'Romaine lettuce, croutons, Parmesan cheese, and Caesar dressing.',
    price: 9.99,
    category: 'appetizer',
    image: '/images/menu/caesar-salad.jpg',
    available: true,
  },
  {
    id: '3',
    name: 'Grilled Ribeye Steak',
    description: '12 oz ribeye steak grilled to perfection.',
    price: 24.99,
    category: 'mainCourse',
    image: '/images/menu/ribeye-steak.jpg',
    available: true,
  },
  {
    id: '4',
    name: 'Chocolate Lava Cake',
    description: 'Warm chocolate cake with a molten chocolate center.',
    price: 7.99,
    category: 'dessert',
    image: '/images/menu/chocolate-lava-cake.jpg',
    available: true,
  },
  {
    id: '5',
    name: 'Spaghetti Carbonara',
    description: 'Spaghetti with a creamy sauce, pancetta, and Parmesan cheese.',
    price: 14.99,
    category: 'mainCourse',
    image: '/images/menu/spaghetti-carbonara.jpg',
    available: true,
  },
];

interface MenuPageProps {
  params: Promise<{
    slugShop: string;
    slugBranch: string;
  }>;
}

export default function MenuPage({ params }: MenuPageProps) {
  const { slugShop, slugBranch } = React.use(params);
  const [branchWithShop] = useState(getBranchWithShop(slugShop, slugBranch));
  const [isLoading, setIsLoading] = useState(true);
  const [menuItems, setMenuItems] = useState(initialMenuItems);
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('all');

  // Simulate loading
  useEffect(() => {
    const timer = setTimeout(() => {
      setIsLoading(false);
    }, 1000);

    return () => clearTimeout(timer);
  }, []);

  if (isLoading) {
    return <AppLoading type="restaurant" size="lg" />;
  }

  if (!branchWithShop) {
    return (
      <div className="font-be-vietnam">
        <div className="flex items-center mb-6">
          <Link href={`/app/restaurant/${slugShop}`}>
            <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Restaurant
            </Button>
          </Link>
        </div>
        <div className="text-center py-12">
          <h1 className="text-[#181510] text-[32px] font-bold leading-tight mb-2">Branch Not Found</h1>
          <p className="text-[#8a745c] text-sm">The branch you are looking for does not exist.</p>
        </div>
      </div>
    );
  }

  // Filter menu items based on search term and active tab
  const filteredMenuItems = menuItems.filter(item => {
    const matchesSearch = item.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         item.description.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = activeTab === 'all' || item.category === activeTab;
    return matchesSearch && matchesCategory;
  });

  const { branch, shop } = branchWithShop;

  return (
    <div className="p-6 font-be-vietnam">
      <div className="flex items-center mb-6">
        <Link href={`/app/restaurant/${slugShop}/${slugBranch}/dashboard`}>
          <Button variant="outline" className="border-[#e2dcd4] text-[#181510]">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Dashboard
          </Button>
        </Link>
      </div>

      <div className="flex flex-wrap justify-between gap-3 mb-6">
        <div>
          <h1 className="text-[#181510] tracking-light text-[32px] font-bold leading-tight">Menu Items</h1>
          <p className="text-[#8a745c] text-sm">
            Manage menu items for {shop.name} - {branch.name}
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/ai-generate`}>
            <Button
              variant="outline"
              className="bg-[#e5ccb2] hover:bg-[#d6bd9e] text-[#181510] border-none"
            >
              <Sparkles className="mr-2 h-4 w-4" />
              AI Generate
            </Button>
          </Link>
          <Link href={`/app/restaurant/${slugShop}/${slugBranch}/menu/add`}>
            <Button className="bg-[#8a745c] hover:bg-[#6d5a48] text-white">
              <Plus className="mr-2 h-4 w-4" />
              Add Item
            </Button>
          </Link>
        </div>
      </div>

      <div className="mb-6">
        <Input
          placeholder="Search menu items..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="max-w-md"
        />
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="bg-[#f1edea] p-1">
          <TabsTrigger value="all" className="data-[state=active]:bg-white">All</TabsTrigger>
          <TabsTrigger value="appetizer" className="data-[state=active]:bg-white">Appetizers</TabsTrigger>
          <TabsTrigger value="mainCourse" className="data-[state=active]:bg-white">Main Courses</TabsTrigger>
          <TabsTrigger value="dessert" className="data-[state=active]:bg-white">Desserts</TabsTrigger>
          <TabsTrigger value="beverage" className="data-[state=active]:bg-white">Beverages</TabsTrigger>
        </TabsList>

        <TabsContent value={activeTab} className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {filteredMenuItems.length > 0 ? (
              filteredMenuItems.map((item) => (
                <Link
                  key={item.id}
                  href={`/app/restaurant/${slugShop}/${slugBranch}/menu/${item.id}`}
                  className="block"
                >
                  <div className="bg-white rounded-lg overflow-hidden border border-[#e5e1dc] hover:shadow-md transition-shadow">
                    <MenuItemImage src={item.image} alt={item.name} />
                    <div className="p-4">
                      <div className="flex justify-between items-start mb-2">
                        <h3 className="font-medium text-[#181510]">{item.name}</h3>
                        <span className="text-[#8a745c] font-medium">${item.price.toFixed(2)}</span>
                      </div>
                      <p className="text-sm text-[#8a745c] line-clamp-2">{item.description}</p>
                      <div className="mt-3 flex justify-between items-center">
                        <span className="text-xs px-2 py-1 bg-[#f1edea] rounded-full text-[#8a745c]">
                          {item.category === 'mainCourse' ? 'Main Course' :
                           item.category.charAt(0).toUpperCase() + item.category.slice(1)}
                        </span>
                        <span className={`text-xs px-2 py-1 rounded-full ${
                          item.available
                            ? 'bg-green-100 text-green-800'
                            : 'bg-red-100 text-red-800'
                        }`}>
                          {item.available ? 'Available' : 'Unavailable'}
                        </span>
                      </div>
                    </div>
                  </div>
                </Link>
              ))
            ) : (
              <div className="col-span-full text-center py-10 text-[#8a745c]">
                No menu items found. Try adjusting your search or add new items.
              </div>
            )}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
